package com.datayes.script

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

/**
 * ScriptService单元测试
 * 
 * 专注于测试纯函数validateSingleSqlStatement的行为
 */
class ScriptServiceTest {

    @Test
    fun `validateSingleSqlStatement should accept single SELECT statement`() {
        val sql = "SELECT * FROM users WHERE id = 1"
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should accept single SELECT statement with trailing semicolon`() {
        val sql = "SELECT * FROM users WHERE id = 1;"
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should accept INSERT statement`() {
        val sql = "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>')"
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should accept UPDATE statement`() {
        val sql = "UPDATE users SET name = 'Jane' WHERE id = 1"
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should accept DELETE statement`() {
        val sql = "DELETE FROM users WHERE id = 1"
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should reject multiple statements with semicolons`() {
        val sql = """
            SELECT * FROM users;
            DELETE FROM users WHERE id = 1;
        """.trimIndent()
        
        val exception = assertThrows<IllegalArgumentException> {
            ScriptService.validateSingleSqlStatement(sql)
        }
        
        assertEquals(
            "SQL脚本不能包含多条语句，检测到 2 个分号 (SQL script cannot contain multiple statements, detected 2 semicolons)",
            exception.message
        )
    }

    @Test
    fun `validateSingleSqlStatement should reject statements with semicolon in middle`() {
        val sql = "SELECT * FROM users; SELECT * FROM orders"
        
        val exception = assertThrows<IllegalArgumentException> {
            ScriptService.validateSingleSqlStatement(sql)
        }
        
        assertEquals(
            "SQL脚本不能包含多条语句，分号不在语句末尾 (SQL script cannot contain multiple statements, semicolon not at statement end)",
            exception.message
        )
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with line comments`() {
        val sql = """
            -- This is a comment
            SELECT * FROM users 
            -- WHERE id = 1; this semicolon is in a comment
            WHERE id = 2;
        """.trimIndent()
        
        // 应该不抛出异常，因为注释中的分号应该被忽略
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with block comments`() {
        val sql = """
            /* This is a block comment 
               with a semicolon; inside */
            SELECT * FROM users WHERE id = 1;
        """.trimIndent()
        
        // 应该不抛出异常，因为注释中的分号应该被忽略
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with string literals containing semicolons`() {
        val sql = """SELECT * FROM users WHERE description = 'This is a test; with semicolon' AND id = 1;"""
        
        // 应该不抛出异常，因为字符串字面量中的分号应该被忽略
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with double quoted strings containing semicolons`() {
        val sql = """SELECT * FROM users WHERE description = "This is a test; with semicolon" AND id = 1;"""
        
        // 应该不抛出异常，因为字符串字面量中的分号应该被忽略
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle complex SQL with mixed comments and strings`() {
        val sql = """
            -- Comment with semicolon;
            SELECT 
                u.name,
                u.email,
                'Status: active; priority: high' as status_info -- another comment;
            FROM users u
            /* Block comment with semicolon;
               and more text; here */
            WHERE u.active = 1;
        """.trimIndent()
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should reject multiple statements even with comments and strings`() {
        val sql = """
            -- First query
            SELECT * FROM users WHERE name = 'test; with semicolon';
            /* Comment between queries; */
            DELETE FROM logs WHERE date < '2023-01-01';
        """.trimIndent()
        
        val exception = assertThrows<IllegalArgumentException> {
            ScriptService.validateSingleSqlStatement(sql)
        }
        
        assertEquals(
            "SQL脚本不能包含多条语句，检测到 2 个分号 (SQL script cannot contain multiple statements, detected 2 semicolons)",
            exception.message
        )
    }

    @Test
    fun `validateSingleSqlStatement should handle empty or whitespace-only input`() {
        val sql = "   \n\t  "
        
        // 应该不抛出异常，空内容被认为是有效的
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with escaped characters`() {
        val sql = """SELECT * FROM users WHERE name = 'O\'Brien; Test' AND id = 1;"""
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }

    @Test
    fun `validateSingleSqlStatement should handle SQL with escaped quotes in different scenarios`() {
        // Test escaped single quote within single-quoted string
        val sql1 = """SELECT * FROM users WHERE name = 'John\'s House; Test';"""
        ScriptService.validateSingleSqlStatement(sql1)
        
        // Test escaped double quote within double-quoted string  
        val sql2 = """SELECT * FROM users WHERE name = "She said \"Hello; World\"";"""
        ScriptService.validateSingleSqlStatement(sql2)
        
        // Test multiple escaped quotes
        val sql3 = """SELECT * FROM users WHERE description = 'It\'s a \"test; case\" here';"""
        ScriptService.validateSingleSqlStatement(sql3)
    }

    @Test
    fun `validateSingleSqlStatement should handle multiline SQL without semicolons`() {
        val sql = """
            SELECT 
                u.id,
                u.name,
                u.email
            FROM users u
            WHERE u.active = 1
            ORDER BY u.name
        """.trimIndent()
        
        // 应该不抛出异常
        ScriptService.validateSingleSqlStatement(sql)
    }
}