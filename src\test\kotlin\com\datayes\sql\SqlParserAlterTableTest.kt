package com.datayes.sql

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

/**
 * Unit tests for SqlParser.parseDataModification method with ALTER TABLE statements
 * Following TDD approach - tests written first, then implementation
 */
class SqlParserAlterTableTest {

    @Test
    @DisplayName("Should parse basic ALTER TABLE ADD COLUMN statement")
    fun testParseBasicAlterTableAddColumn() {
        // Given - Basic ALTER TABLE ADD COLUMN statement
        val alterSql = "ALTER TABLE users ADD COLUMN email VARCHAR(255)"
        
        println("a1b2c3d4 | Testing basic ALTER TABLE ADD COLUMN: '$alterSql'")
        
        // When
        val result = SqlParser.parseDataModification(alterSql)
        
        // Then - TDD: Test what we expect ALTER TABLE support to provide
        assertThat(result).isNotNull
        
        // Verify target table (table being altered)
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()
        
        // Verify target columns (new column being added)
        assertThat(result.targetColumns).isNotNull
        assertThat(result.targetColumns).containsExactly("email")
        
        // Verify no source tables for ALTER TABLE (it's a schema modification)
        assertThat(result.sourceTables).isEmpty()
        
        // Verify no source columns for simple ADD COLUMN
        assertThat(result.sourceColumns).isEmpty()
        
        // Verify column mappings (for ALTER TABLE, this represents the new column definition)
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("email")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)
        
        println("e5f6g7h8 | ✅ Basic ALTER TABLE ADD COLUMN parsed successfully")
        println("i9j0k1l2 | Target table: ${result.targetTable.name}")
        println("m3n4o5p6 | New column: ${result.targetColumns}")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE ADD COLUMN with different data types")
    fun testParseAlterTableAddColumnWithDifferentTypes() {
        val testCases = listOf(
            "ALTER TABLE products ADD COLUMN price DECIMAL(10,2)" to "price",
            "ALTER TABLE orders ADD COLUMN order_date DATE" to "order_date", 
            "ALTER TABLE customers ADD COLUMN is_active BOOLEAN" to "is_active",
            "ALTER TABLE logs ADD COLUMN log_id INT" to "log_id",
            "ALTER TABLE documents ADD COLUMN content TEXT" to "content"
        )
        
        testCases.forEachIndexed { index, (sql, expectedColumn) ->
            println("q7r8s9t0 | Test case ${index + 1}: '$sql'")
            
            // When
            val result = SqlParser.parseDataModification(sql)
            
            // Then
            assertThat(result).isNotNull
            assertThat(result.targetColumns).containsExactly(expectedColumn)
            assertThat(result.columnMappings).hasSize(1)
            assertThat(result.columnMappings[0].targetColumnName).isEqualTo(expectedColumn)
            
            println("u1v2w3x4 |   ✅ Successfully parsed column: $expectedColumn")
        }
        
        println("y5z6a7b8 | 📋 All data type variations parsed successfully")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE ADD COLUMN with schema-qualified table")
    fun testParseAlterTableAddColumnWithSchema() {
        // Given - ALTER TABLE with schema prefix
        val alterSql = "ALTER TABLE myschema.users ADD COLUMN phone VARCHAR(20)"
        
        println("c9d0e1f2 | Testing ALTER TABLE with schema: '$alterSql'")
        
        // When
        val result = SqlParser.parseDataModification(alterSql)
        
        // Then
        assertThat(result).isNotNull
        
        // Verify target table with schema
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("myschema")
        assertThat(result.targetTable.alias).isNull()
        
        // Verify new column
        assertThat(result.targetColumns).containsExactly("phone")
        
        println("g3h4i5j6 | ✅ ALTER TABLE with schema parsed successfully")
        println("k7l8m9n0 | Schema: ${result.targetTable.schemaOrDatabase}")
        println("o1p2q3r4 | Table: ${result.targetTable.name}")
        println("s5t6u7v8 | New column: ${result.targetColumns}")
    }

    @Test
    @DisplayName("Should handle quoted table and column names")
    fun testParseAlterTableWithQuotedNames() {
        val testCases = listOf(
            "ALTER TABLE \"users\" ADD COLUMN \"email_address\" VARCHAR(255)" to Pair("users", "email_address"),
            "ALTER TABLE `products` ADD COLUMN `unit_price` DECIMAL(10,2)" to Pair("products", "unit_price"),
            "ALTER TABLE [orders] ADD COLUMN [created_at] TIMESTAMP" to Pair("orders", "created_at")
        )
        
        testCases.forEachIndexed { index, (sql, expected) ->
            println("w9x0y1z2 | Test case ${index + 1}: '$sql'")
            
            try {
                // When
                val result = SqlParser.parseDataModification(sql)
                
                // Then
                assertThat(result).isNotNull
                assertThat(result.targetTable.name).isEqualTo(expected.first)
                assertThat(result.targetColumns).containsExactly(expected.second)
                
                println("a3b4c5d6 |   ✅ Successfully parsed quoted names: table=${expected.first}, column=${expected.second}")
            } catch (e: Exception) {
                // Some quote styles might not be supported - that's acceptable
                println("e7f8g9h0 |   ⚠️  Quoted name style not supported: ${e.message}")
            }
        }
    }

    @Test
    @DisplayName("Should handle case insensitive ALTER TABLE statements")
    fun testParseAlterTableCaseInsensitive() {
        val testCases = listOf(
            "alter table users add column email varchar(255)",
            "ALTER TABLE Users ADD COLUMN Email VARCHAR(255)",
            "Alter Table USERS Add Column EMAIL Varchar(255)"
        )
        
        testCases.forEachIndexed { index, sql ->
            println("i1j2k3l4 | Test case ${index + 1}: '$sql'")
            
            // When
            val result = SqlParser.parseDataModification(sql)
            
            // Then
            assertThat(result).isNotNull
            assertThat(result.targetTable.name).isEqualToIgnoringCase("users")
            assertThat(result.targetColumns).hasSize(1)
            assertThat(result.targetColumns!![0]).isEqualToIgnoringCase("email")
            
            println("m5n6o7p8 |   ✅ Case insensitive parsing successful")
        }
    }

    @Test
    @DisplayName("Should validate ALTER TABLE statement format")
    fun testAlterTableStatementValidation() {
        // Given - Various invalid ALTER TABLE statements that should be rejected
        val invalidAlterStatements = listOf(
            "ALTER", // Incomplete
            "ALTER TABLE", // Missing table name
            "ALTER TABLE users", // Missing ADD COLUMN
            "ALTER TABLE users ADD", // Missing COLUMN keyword
            "ALTER TABLE users ADD COLUMN", // Missing column name
            "ALTER TABLE users ADD COLUMN email", // Missing data type
            "ADD COLUMN email VARCHAR(255)", // Not an ALTER TABLE statement
            "ALTER users ADD COLUMN email VARCHAR(255)" // Missing TABLE keyword
        )
        
        println("q9r0s1t2 | Testing ALTER TABLE statement validation")
        
        invalidAlterStatements.forEachIndexed { index, alterSql ->
            println("u3v4w5x6 | Test case ${index + 1}: '$alterSql'")
            
            // When & Then - All should fail with SqlParsingException
            assertThatThrownBy {
                SqlParser.parseDataModification(alterSql)
            }.isInstanceOf(SqlParsingException::class.java)
            
            println("y7z8a9b0 |   ✅ Correctly rejected invalid ALTER TABLE statement")
        }
        
        println("c1d2e3f4 | 📋 ALTER TABLE validation working correctly")
    }

    @Test
    @DisplayName("Should demonstrate expected DataModificationResult structure for ALTER TABLE")
    fun testExpectedAlterTableResultStructure() {
        // This test documents what the DataModificationResult should contain for ALTER TABLE statements
        
        println("g5h6i7j8 | 📋 Expected DataModificationResult structure for ALTER TABLE statements:")
        println("k9l0m1n2 | ")
        println("o3p4q5r6 | For ALTER TABLE statement: 'ALTER TABLE users ADD COLUMN email VARCHAR(255)'")
        println("s7t8u9v0 | Expected DataModificationResult:")
        println("w1x2y3z4 |   - targetTable: TableReference(schemaOrDatabase=null, name='users', alias=null)")
        println("a5b6c7d8 |   - targetColumns: ['email'] (new column being added)")
        println("e9f0g1h2 |   - sourceTables: [] (empty - ALTER TABLE is schema modification)")
        println("i3j4k5l6 |   - sourceColumns: [] (empty - no source data for ADD COLUMN)")
        println("m7n8o9p0 |   - columnMappings: [ColumnMapping(sourceColumn=null, targetColumnName='email', targetColumnIndex=0)]")
        println("q1r2s3t4 | ")
        println("u5v6w7x8 | Note: ALTER TABLE ADD COLUMN has unique characteristics:")
        println("y9z0a1b2 |   1. Target table is the table being modified")
        println("c3d4e5f6 |   2. Target columns represent new columns being added")
        println("g7h8i9j0 |   3. No source tables or columns (schema modification, not data movement)")
        println("k1l2m3n4 |   4. Column mappings represent the new column definitions")
        
        // This is a documentation test - no assertions needed
        assertThat(true).isTrue() // Just to make it a valid test
    }

    @Test
    @DisplayName("Should handle ALTER TABLE ADD COLUMN with column constraints")
    fun testParseAlterTableAddColumnWithConstraints() {
        val testCases = listOf(
            "ALTER TABLE users ADD COLUMN email VARCHAR(255) NOT NULL" to "email",
            "ALTER TABLE products ADD COLUMN price DECIMAL(10,2) DEFAULT 0.00" to "price",
            "ALTER TABLE orders ADD COLUMN status VARCHAR(50) NOT NULL DEFAULT 'pending'" to "status"
        )
        
        testCases.forEachIndexed { index, (sql, expectedColumn) ->
            println("o5p6q7r8 | Test case ${index + 1}: '$sql'")
            
            try {
                // When
                val result = SqlParser.parseDataModification(sql)
                
                // Then
                assertThat(result).isNotNull
                assertThat(result.targetColumns).containsExactly(expectedColumn)
                
                println("s9t0u1v2 |   ✅ Successfully parsed column with constraints: $expectedColumn")
            } catch (e: Exception) {
                // Complex constraints might not be fully supported initially - that's acceptable
                println("w3x4y5z6 |   ⚠️  Column constraints not fully supported: ${e.message}")
            }
        }
    }

    @Test
    @DisplayName("Should parse ALTER TABLE ALTER COLUMN statement")
    fun testParseAlterTableAlterColumn() {
        // Given - ALTER TABLE ALTER COLUMN statement
        val alterSql = "ALTER TABLE cmdb.test1 ALTER COLUMN customer_number TYPE DECIMAL"
        
        println("a7b8c9d0 | Testing ALTER TABLE ALTER COLUMN: '$alterSql'")
        
        // When
        val result = SqlParser.parseDataModification(alterSql)
        
        // Then - For ALTER COLUMN, we only care about table name and column name
        assertThat(result).isNotNull
        
        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("test1")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("cmdb")
        assertThat(result.targetTable.alias).isNull()
        
        // Verify target columns (column being altered)
        assertThat(result.targetColumns).isNotNull
        assertThat(result.targetColumns).containsExactly("customer_number")
        
        // Verify no source tables for ALTER COLUMN (it's a schema modification)
        assertThat(result.sourceTables).isEmpty()
        
        // Verify no source columns for ALTER COLUMN
        assertThat(result.sourceColumns).isEmpty()
        
        // Verify column mappings
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("customer_number")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)
        
        println("e1f2g3h4 | ✅ ALTER TABLE ALTER COLUMN parsed successfully")
        println("i5j6k7l8 | Target table: ${result.targetTable.schemaOrDatabase}.${result.targetTable.name}")
        println("m9n0o1p2 | Altered column: ${result.targetColumns}")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE ALTER COLUMN with different variations")
    fun testParseAlterTableAlterColumnVariations() {
        val testCases = listOf(
            "ALTER TABLE products ALTER COLUMN price TYPE DECIMAL(10,2)" to "price",
            "ALTER TABLE users ALTER COLUMN email TYPE VARCHAR(500)" to "email", 
            "ALTER TABLE orders ALTER COLUMN order_date TYPE DATE" to "order_date",
            "ALTER TABLE inventory.items ALTER COLUMN quantity TYPE INTEGER" to "quantity"
        )
        
        testCases.forEachIndexed { index, (sql, expectedColumn) ->
            println("q3r4s5t6 | Test case ${index + 1}: '$sql'")
            
            // When
            val result = SqlParser.parseDataModification(sql)
            
            // Then
            assertThat(result).isNotNull
            assertThat(result.targetColumns).containsExactly(expectedColumn)
            assertThat(result.columnMappings).hasSize(1)
            assertThat(result.columnMappings[0].targetColumnName).isEqualTo(expectedColumn)
            
            println("u7v8w9x0 |   ✅ Successfully parsed ALTER COLUMN: $expectedColumn")
        }
        
        println("y1z2a3b4 | 📋 All ALTER COLUMN variations parsed successfully")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE DROP COLUMN statement")
    fun testParseAlterTableDropColumn() {
        // Given - ALTER TABLE DROP COLUMN statement
        val alterSql = "ALTER TABLE users DROP COLUMN email"
        
        println("c5d6e7f8 | Testing ALTER TABLE DROP COLUMN: '$alterSql'")
        
        // When
        val result = SqlParser.parseDataModification(alterSql)
        
        // Then - For DROP COLUMN, we only care about table name and column name
        assertThat(result).isNotNull
        
        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()
        
        // Verify target columns (column being dropped)
        assertThat(result.targetColumns).isNotNull
        assertThat(result.targetColumns).containsExactly("email")
        
        // Verify no source tables for DROP COLUMN (it's a schema modification)
        assertThat(result.sourceTables).isEmpty()
        
        // Verify no source columns for DROP COLUMN
        assertThat(result.sourceColumns).isEmpty()
        
        // Verify column mappings
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("email")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)
        
        println("g9h0i1j2 | ✅ ALTER TABLE DROP COLUMN parsed successfully")
        println("k3l4m5n6 | Target table: ${result.targetTable.name}")
        println("o7p8q9r0 | Dropped column: ${result.targetColumns}")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE DROP COLUMN with different variations")
    fun testParseAlterTableDropColumnVariations() {
        val testCases = listOf(
            "ALTER TABLE products DROP COLUMN price" to "price",
            "ALTER TABLE users DROP COLUMN email_address" to "email_address", 
            "ALTER TABLE orders DROP COLUMN created_at" to "created_at",
            "ALTER TABLE inventory.items DROP COLUMN description" to "description",
            "ALTER TABLE cmdb.test1 DROP COLUMN customer_number" to "customer_number"
        )
        
        testCases.forEachIndexed { index, (sql, expectedColumn) ->
            println("s1t2u3v4 | Test case ${index + 1}: '$sql'")
            
            // When
            val result = SqlParser.parseDataModification(sql)
            
            // Then
            assertThat(result).isNotNull
            assertThat(result.targetColumns).containsExactly(expectedColumn)
            assertThat(result.columnMappings).hasSize(1)
            assertThat(result.columnMappings[0].targetColumnName).isEqualTo(expectedColumn)
            
            println("w5x6y7z8 |   ✅ Successfully parsed DROP COLUMN: $expectedColumn")
        }
        
        println("a9b0c1d2 | 📋 All DROP COLUMN variations parsed successfully")
    }

    @Test
    @DisplayName("Should parse ALTER TABLE DROP COLUMN with quoted column names")
    fun testParseAlterTableDropColumnWithQuotes() {
        val testCases = listOf(
            "ALTER TABLE users DROP COLUMN \"email_address\"" to "email_address",
            "ALTER TABLE products DROP COLUMN `unit_price`" to "unit_price",
            "ALTER TABLE orders DROP COLUMN [created_at]" to "created_at"
        )
        
        testCases.forEachIndexed { index, (sql, expectedColumn) ->
            println("e3f4g5h6 | Test case ${index + 1}: '$sql'")
            
            try {
                // When
                val result = SqlParser.parseDataModification(sql)
                
                // Then
                assertThat(result).isNotNull
                assertThat(result.targetColumns).containsExactly(expectedColumn)
                
                println("i7j8k9l0 |   ✅ Successfully parsed quoted DROP COLUMN: $expectedColumn")
            } catch (e: Exception) {
                // Some quote styles might not be supported - that's acceptable
                println("m1n2o3p4 |   ⚠️  Quoted column name style not supported: ${e.message}")
            }
        }
    }
}
