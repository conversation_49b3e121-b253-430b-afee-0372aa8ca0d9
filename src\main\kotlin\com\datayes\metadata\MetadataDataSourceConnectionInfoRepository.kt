package com.datayes.metadata

import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

/**
 * 元数据数据源连接信息Repository (Metadata Data Source Connection Info Repository)
 *
 * 使用 Spring Data JDBC 自动生成的 Repository
 */
@Repository
interface MetadataDataSourceConnectionInfoRepository : CrudRepository<MetadataDataSourceConnectionInfo, Long> {
    
    /**
     * 根据数据源ID查找连接信息
     */
    fun findByDataSourceId(dataSourceId: Long): List<MetadataDataSourceConnectionInfo>
    
    /**
     * 根据活跃状态查找连接信息
     */
    fun findByActiveFlag(activeFlag: Boolean): List<MetadataDataSourceConnectionInfo>
    
    /**
     * 根据数据源ID和活跃状态查找连接信息
     */
    fun findByDataSourceIdAndActiveFlag(dataSourceId: Long, activeFlag: Boolean): List<MetadataDataSourceConnectionInfo>
}