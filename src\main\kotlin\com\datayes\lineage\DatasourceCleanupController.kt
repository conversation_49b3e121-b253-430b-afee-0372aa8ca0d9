package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime

/**
 * 数据源清理控制器 (Datasource Cleanup Controller)
 * 
 * 用于清理重复的数据源记录及其相关的血缘数据
 * Handles cleanup of duplicate datasource records and their related lineage data
 */
@RestController
@RequestMapping("/api/admin/datasource-cleanup")
class DatasourceCleanupController(
    private val jdbcTemplate: JdbcTemplate
) {
    
    private val logger = LoggerFactory.getLogger(DatasourceCleanupController::class.java)
    
    /**
     * 分析重复的数据源并生成清理SQL (Analyze duplicate datasources and generate cleanup SQL)
     */
    @PostMapping("/analyze")
    fun analyzeDuplicateDatasources(@RequestParam(defaultValue = "false") sqlOnly: Boolean): Any {
        logger.info("4f3e2d1c | 开始分析重复数据源")
        
        try {
            // Step 1: 找到所有重复的数据源组 (Find all duplicate datasource groups)
            val duplicateGroups = findDuplicateDatasourceGroups()
            logger.info("5e4d3c2b | 找到重复数据源组: ${duplicateGroups.size}")
            
            if (duplicateGroups.isEmpty()) {
                return if (sqlOnly) {
                    "-- 未发现重复的数据源记录 (No duplicate datasource records found)\n-- 无需执行清理操作 (No cleanup required)"
                } else {
                    DatasourceCleanupAnalysis(
                        duplicateGroups = emptyList(),
                        affectedCounts = AffectedCounts(0, 0, 0, 0),
                        deleteStatements = emptyList(),
                        summary = "未发现重复的数据源记录 (No duplicate datasource records found)"
                    )
                }
            }
            
            // Step 2: 为每个组确定保留哪个记录 (Determine which record to keep for each group)
            val cleanupPlan = duplicateGroups.map { group ->
                val canonical = selectCanonicalDatasource(group)
                val duplicates = group.datasources.filter { it.id != canonical.id }
                
                DatasourceCleanupGroup(
                    host = group.host,
                    port = group.port,
                    databaseName = group.databaseName,
                    canonicalDatasource = canonical,
                    duplicateDatasources = duplicates,
                    affectedTables = countAffectedTables(duplicates.map { it.id }),
                    affectedColumns = countAffectedColumns(duplicates.map { it.id }),
                    affectedRelationships = countAffectedRelationships(duplicates.map { it.id })
                )
            }
            
            // Step 3: 计算总的影响统计 (Calculate total impact statistics)
            val affectedCounts = AffectedCounts(
                duplicateDatasources = cleanupPlan.sumOf { it.duplicateDatasources.size },
                affectedTables = cleanupPlan.sumOf { it.affectedTables },
                affectedColumns = cleanupPlan.sumOf { it.affectedColumns },
                affectedRelationships = cleanupPlan.sumOf { it.affectedRelationships }
            )
            
            // Step 4: 生成删除SQL语句 (Generate DELETE SQL statements)
            val deleteStatements = generateDeleteStatements(cleanupPlan)
            
            val summary = buildString {
                append("发现 ${cleanupPlan.size} 个重复数据源组，")
                append("涉及 ${affectedCounts.duplicateDatasources} 个重复数据源、")
                append("${affectedCounts.affectedTables} 个表、")
                append("${affectedCounts.affectedColumns} 个列、")
                append("${affectedCounts.affectedRelationships} 个血缘关系")
            }
            
            logger.info("6d5c4b3a | 分析完成: $summary")
            
            return if (sqlOnly) {
                generateSqlOnlyResponse(cleanupPlan, deleteStatements, affectedCounts)
            } else {
                DatasourceCleanupAnalysis(
                    duplicateGroups = cleanupPlan,
                    affectedCounts = affectedCounts,
                    deleteStatements = deleteStatements,
                    summary = summary
                )
            }
            
        } catch (e: Exception) {
            logger.error("7c6b5a49 | 分析重复数据源时发生错误", e)
            throw RuntimeException("分析重复数据源失败: ${e.message}", e)
        }
    }
    
    /**
     * 查找重复的数据源组 (Find duplicate datasource groups)
     */
    private fun findDuplicateDatasourceGroups(): List<DuplicateDatasourceGroup> {
        val sql = """
            SELECT host, port, database_name, 
                   COUNT(*) as duplicate_count
            FROM lineage_datasources 
            GROUP BY host, port, database_name 
            HAVING COUNT(*) > 1
            ORDER BY host, port, database_name
        """
        
        val duplicateGroups = jdbcTemplate.query(sql) { rs, _ ->
            DuplicateGroupInfo(
                host = rs.getString("host"),
                port = rs.getInt("port"),
                databaseName = rs.getString("database_name"),
                duplicateCount = rs.getInt("duplicate_count")
            )
        }
        
        // 为每个重复组获取详细的数据源信息
        return duplicateGroups.map { groupInfo ->
            val datasources = getDatasourcesForGroup(groupInfo.host, groupInfo.port, groupInfo.databaseName)
            DuplicateDatasourceGroup(
                host = groupInfo.host,
                port = groupInfo.port,
                databaseName = groupInfo.databaseName,
                datasources = datasources
            )
        }
    }
    
    /**
     * 获取指定连接信息的所有数据源 (Get all datasources for specified connection info)
     */
    private fun getDatasourcesForGroup(host: String, port: Int, databaseName: String): List<DatasourceInfo> {
        val sql = """
            SELECT id, datasource_name, db_type, status, created_at, updated_at
            FROM lineage_datasources 
            WHERE host = ? AND port = ? AND database_name = ?
            ORDER BY created_at ASC
        """
        
        return jdbcTemplate.query(sql, { rs, _ ->
            DatasourceInfo(
                id = rs.getLong("id"),
                datasourceName = rs.getString("datasource_name"),
                dbType = rs.getString("db_type"),
                host = host,
                port = port,
                databaseName = databaseName,
                status = rs.getString("status"),
                createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
                updatedAt = rs.getTimestamp("updated_at").toLocalDateTime()
            )
        }, host, port, databaseName)
    }
    
    /**
     * 选择保留的规范数据源 (Select canonical datasource to keep)
     * 选择最早创建的记录
     */
    private fun selectCanonicalDatasource(group: DuplicateDatasourceGroup): DatasourceInfo {
        return group.datasources.minByOrNull { it.createdAt }!!
    }
    
    /**
     * 统计受影响的表数量 (Count affected tables)
     */
    private fun countAffectedTables(datasourceIds: List<Long>): Int {
        if (datasourceIds.isEmpty()) return 0
        
        val sql = """
            SELECT COUNT(*) FROM lineage_tables 
            WHERE datasource_id IN (${datasourceIds.joinToString(",")})
        """
        
        return jdbcTemplate.queryForObject(sql, Int::class.java) ?: 0
    }
    
    /**
     * 统计受影响的列数量 (Count affected columns)
     */
    private fun countAffectedColumns(datasourceIds: List<Long>): Int {
        if (datasourceIds.isEmpty()) return 0
        
        val sql = """
            SELECT COUNT(*) FROM lineage_columns lc
            INNER JOIN lineage_tables lt ON lc.table_id = lt.id
            WHERE lt.datasource_id IN (${datasourceIds.joinToString(",")})
        """
        
        return jdbcTemplate.queryForObject(sql, Int::class.java) ?: 0
    }
    
    /**
     * 统计受影响的血缘关系数量 (Count affected relationships)
     */
    private fun countAffectedRelationships(datasourceIds: List<Long>): Int {
        if (datasourceIds.isEmpty()) return 0
        
        val sql = """
            SELECT COUNT(DISTINCT lr.id) FROM lineage_relationships lr
            INNER JOIN lineage_tables lt_source ON lr.source_table_id = lt_source.id
            INNER JOIN lineage_tables lt_target ON lr.target_table_id = lt_target.id
            WHERE lt_source.datasource_id IN (${datasourceIds.joinToString(",")})
               OR lt_target.datasource_id IN (${datasourceIds.joinToString(",")})
        """
        
        return jdbcTemplate.queryForObject(sql, Int::class.java) ?: 0
    }
    
    /**
     * 生成删除SQL语句 (Generate DELETE SQL statements)
     */
    private fun generateDeleteStatements(cleanupPlan: List<DatasourceCleanupGroup>): List<DeleteStatement> {
        val statements = mutableListOf<DeleteStatement>()
        
        cleanupPlan.forEach { group ->
            val duplicateIds = group.duplicateDatasources.map { it.id }
            
            if (duplicateIds.isNotEmpty()) {
                // 1. 删除血缘关系 (Delete lineage relationships)
                statements.add(DeleteStatement(
                    order = statements.size + 1,
                    category = "lineage_relationships",
                    description = "删除引用重复数据源表的血缘关系 (${group.host}:${group.port}/${group.databaseName})",
                    sql = "DELETE lr FROM lineage_relationships lr INNER JOIN lineage_tables lt_source ON lr.source_table_id = lt_source.id WHERE lt_source.datasource_id IN (${duplicateIds.joinToString(",")});",
                    affectedCount = group.affectedRelationships
                ))
                
                statements.add(DeleteStatement(
                    order = statements.size + 1,
                    category = "lineage_relationships",
                    description = "删除目标为重复数据源表的血缘关系 (${group.host}:${group.port}/${group.databaseName})",
                    sql = "DELETE lr FROM lineage_relationships lr INNER JOIN lineage_tables lt_target ON lr.target_table_id = lt_target.id WHERE lt_target.datasource_id IN (${duplicateIds.joinToString(",")});",
                    affectedCount = group.affectedRelationships
                ))
                
                // 2. 删除列 (Delete columns)
                statements.add(DeleteStatement(
                    order = statements.size + 1,
                    category = "lineage_columns",
                    description = "删除重复数据源的列 (${group.host}:${group.port}/${group.databaseName})",
                    sql = "DELETE lc FROM lineage_columns lc INNER JOIN lineage_tables lt ON lc.table_id = lt.id WHERE lt.datasource_id IN (${duplicateIds.joinToString(",")});",
                    affectedCount = group.affectedColumns
                ))
                
                // 3. 删除表 (Delete tables)
                statements.add(DeleteStatement(
                    order = statements.size + 1,
                    category = "lineage_tables",
                    description = "删除重复数据源的表 (${group.host}:${group.port}/${group.databaseName})",
                    sql = "DELETE FROM lineage_tables WHERE datasource_id IN (${duplicateIds.joinToString(",")});",
                    affectedCount = group.affectedTables
                ))
                
                // 4. 删除数据源 (Delete datasources)
                statements.add(DeleteStatement(
                    order = statements.size + 1,
                    category = "lineage_datasources",
                    description = "删除重复的数据源 (${group.host}:${group.port}/${group.databaseName}) - 保留: ${group.canonicalDatasource.dbType}(id=${group.canonicalDatasource.id})",
                    sql = "DELETE FROM lineage_datasources WHERE id IN (${duplicateIds.joinToString(",")});",
                    affectedCount = duplicateIds.size
                ))
            }
        }
        
        return statements
    }
    
    /**
     * 生成仅SQL响应格式 (Generate SQL-only response format)
     */
    private fun generateSqlOnlyResponse(
        cleanupPlan: List<DatasourceCleanupGroup>,
        deleteStatements: List<DeleteStatement>,
        affectedCounts: AffectedCounts
    ): String {
        val sqlBuilder = StringBuilder()
        
        // 添加头部注释
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine("-- 数据源清理SQL脚本 (Datasource Cleanup SQL Script)")
        sqlBuilder.appendLine("-- 生成时间: ${LocalDateTime.now()}")
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine()
        
        // 添加摘要信息
        sqlBuilder.appendLine("-- 清理摘要 (Cleanup Summary):")
        sqlBuilder.appendLine("-- 重复数据源组: ${cleanupPlan.size}")
        sqlBuilder.appendLine("-- 待删除数据源: ${affectedCounts.duplicateDatasources}")
        sqlBuilder.appendLine("-- 影响的表: ${affectedCounts.affectedTables}")
        sqlBuilder.appendLine("-- 影响的列: ${affectedCounts.affectedColumns}")
        sqlBuilder.appendLine("-- 影响的血缘关系: ${affectedCounts.affectedRelationships}")
        sqlBuilder.appendLine()
        
        // 添加保留的数据源信息
        sqlBuilder.appendLine("-- 保留的数据源 (Canonical Datasources to Keep):")
        cleanupPlan.forEach { group ->
            val canonical = group.canonicalDatasource
            sqlBuilder.appendLine("-- ${group.host}:${group.port}/${group.databaseName} -> 保留 ID=${canonical.id} (${canonical.dbType}, created: ${canonical.createdAt})")
        }
        sqlBuilder.appendLine()
        
        // 添加删除的数据源信息
        sqlBuilder.appendLine("-- 待删除的数据源 (Datasources to Delete):")
        cleanupPlan.forEach { group ->
            group.duplicateDatasources.forEach { duplicate ->
                sqlBuilder.appendLine("-- ${group.host}:${group.port}/${group.databaseName} -> 删除 ID=${duplicate.id} (${duplicate.dbType}, created: ${duplicate.createdAt})")
            }
        }
        sqlBuilder.appendLine()
        
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine("-- 执行说明 (Execution Instructions):")
        sqlBuilder.appendLine("-- 1. 请在执行前备份相关数据")
        sqlBuilder.appendLine("-- 2. 按照以下顺序执行SQL语句")
        sqlBuilder.appendLine("-- 3. 建议逐条执行并检查结果")
        sqlBuilder.appendLine("-- 4. 如有问题请立即停止执行")
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine()
        
        // 添加所有DELETE语句
        deleteStatements.forEach { statement ->
            sqlBuilder.appendLine("-- ${statement.order}. ${statement.description}")
            sqlBuilder.appendLine("-- 预计影响行数: ${statement.affectedCount}")
            sqlBuilder.appendLine(statement.sql)
            sqlBuilder.appendLine()
        }
        
        // 添加验证SQL
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine("-- 验证SQL (Verification Queries)")
        sqlBuilder.appendLine("-- ===============================================================")
        sqlBuilder.appendLine()
        
        sqlBuilder.appendLine("-- 验证是否还有重复数据源")
        sqlBuilder.appendLine("SELECT host, port, database_name, COUNT(*) as count FROM lineage_datasources GROUP BY host, port, database_name HAVING COUNT(*) > 1;")
        sqlBuilder.appendLine()
        
        sqlBuilder.appendLine("-- 验证外键完整性")
        sqlBuilder.appendLine("SELECT COUNT(*) as orphaned_tables FROM lineage_tables lt LEFT JOIN lineage_datasources lds ON lt.datasource_id = lds.id WHERE lds.id IS NULL;")
        sqlBuilder.appendLine()
        
        sqlBuilder.appendLine("-- 验证完成")
        sqlBuilder.appendLine("-- ===============================================================")
        
        return sqlBuilder.toString()
    }
    
    // Data classes for the response
    data class DatasourceCleanupAnalysis(
        val duplicateGroups: List<DatasourceCleanupGroup>,
        val affectedCounts: AffectedCounts,
        val deleteStatements: List<DeleteStatement>,
        val summary: String,
        val generatedAt: LocalDateTime = LocalDateTime.now()
    )
    
    data class DatasourceCleanupGroup(
        val host: String,
        val port: Int,
        val databaseName: String,
        val canonicalDatasource: DatasourceInfo,
        val duplicateDatasources: List<DatasourceInfo>,
        val affectedTables: Int,
        val affectedColumns: Int,
        val affectedRelationships: Int
    )
    
    data class DuplicateDatasourceGroup(
        val host: String,
        val port: Int,
        val databaseName: String,
        val datasources: List<DatasourceInfo>
    )
    
    data class DuplicateGroupInfo(
        val host: String,
        val port: Int,
        val databaseName: String,
        val duplicateCount: Int
    )
    
    data class DatasourceInfo(
        val id: Long,
        val datasourceName: String,
        val dbType: String,
        val host: String,
        val port: Int,
        val databaseName: String,
        val status: String,
        val createdAt: LocalDateTime,
        val updatedAt: LocalDateTime
    )
    
    data class AffectedCounts(
        val duplicateDatasources: Int,
        val affectedTables: Int,
        val affectedColumns: Int,
        val affectedRelationships: Int
    )
    
    data class DeleteStatement(
        val order: Int,
        val category: String,
        val description: String,
        val sql: String,
        val affectedCount: Int
    )
}