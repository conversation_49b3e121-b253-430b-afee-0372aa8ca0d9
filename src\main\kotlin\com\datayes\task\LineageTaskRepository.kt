package com.datayes.task

import com.datayes.lineage.JobLineageHashHistory
import com.datayes.lineage.JobProcessingHistoryRepository.Companion.jobLineageHashHistoryRowMapper
import com.datayes.lineage.JobType
import com.datayes.lineage.ProcessingResult
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.PagingAndSortingRepository
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * 血缘任务仓库接口 (Lineage Task Repository Interface)
 *
 * 基于 Spring Data JDBC 的血缘任务数据访问接口
 */
@Repository
interface LineageTaskRepository : CrudRepository<LineageTask, Long>, PagingAndSortingRepository<LineageTask, Long> {

    /**
     * 根据作业键查找任务 (Find task by job key)
     */
    @Query("SELECT * FROM lineage_tasks WHERE job_key = :jobKey")
    fun findByJobKey(jobKey: String): LineageTask?

    /**
     * 根据状态查找任务 (Find tasks by status)
     */
    @Query("SELECT * FROM lineage_tasks WHERE task_status IN (:statuses)")
    fun findByTaskStatusIn(statuses: List<TaskStatus>): List<LineageTask>

    /**
     * 查找指定批次的任务 (Find tasks by batch id)
     */
    @Query("SELECT * FROM lineage_tasks WHERE batch_id = :batchId")
    fun findByBatchId(batchId: String): List<LineageTask>

    /**
     * 查找活跃任务 (Find enabled tasks)
     */
    @Query("SELECT * FROM lineage_tasks WHERE is_enabled = true")
    fun findActiveTask(): List<LineageTask>

    /**
     * 根据任务类型和启用状态查找任务 (Find tasks by task type and enabled status)
     */
    @Query("SELECT * FROM lineage_tasks WHERE task_type = :taskType AND is_enabled = :isEnabled")
    fun findAllByTaskTypeAndIsEnabled(taskType: TaskType, isEnabled: Boolean): List<LineageTask>

    /**
     * 根据任务名称和任务类型查找任务 (Find task by task name and task type)
     */
    @Query("SELECT * FROM lineage_tasks WHERE task_name = :taskName AND task_type = :taskType")
    fun findByTaskNameAndTaskType(taskName: String, taskType: TaskType): LineageTask?
}

/**
 * 血缘任务自定义仓库实现 (Custom Lineage Task Repository Implementation)
 *
 * 提供复杂查询和分页功能
 */
@Repository
class LineageTaskCustomRepository(private val jdbcTemplate: JdbcTemplate) {

    /**
     * 任务查询条件 (Task Query Criteria)
     */
    data class TaskQueryCriteria(
        val statuses: List<TaskStatus>? = null,
        val taskType: TaskType? = null,
        val taskName: String? = null,
        val createdBy: String? = null,
        val dateFrom: LocalDateTime? = null,
        val dateTo: LocalDateTime? = null,
        val isEnabled: Boolean? = null,
        val batchId: String? = null,
        val sourceId: Long? = null
    )

    /**
     * 分页查询任务 (Find tasks with pagination and criteria)
     */
    fun findTasksWithCriteria(criteria: TaskQueryCriteria, pageable: Pageable): Page<LineageTask> {
        val (whereClause, joinClause) = buildWhereAndJoinClause(criteria)
        val orderClause = buildOrderClause(pageable)

        // 构建查询SQL
        val countSql = "SELECT COUNT(DISTINCT lt.id) FROM lineage_tasks lt $joinClause $whereClause"
        val dataSql = """
            SELECT DISTINCT lt.* FROM lineage_tasks lt
            $joinClause 
            $whereClause 
            $orderClause 
            LIMIT ${pageable.pageSize} OFFSET ${pageable.offset}
        """.trimIndent()

        // 获取参数
        val params = buildParameters(criteria)

        // 执行查询
        val totalElements = jdbcTemplate.queryForObject(countSql, Long::class.java, *params.toTypedArray()) ?: 0L
        val content = if (totalElements > 0) {
            jdbcTemplate.query(dataSql, LineageTaskRowMapper(), *params.toTypedArray())
        } else {
            emptyList()
        }

        return PageImpl(content, pageable, totalElements)
    }

    /**
     * 构建WHERE子句和JOIN子句 (Build WHERE and JOIN clauses)
     */
    private fun buildWhereAndJoinClause(criteria: TaskQueryCriteria): Pair<String, String> {
        val conditions = mutableListOf<String>()
        val joinClause = if (criteria.sourceId != null) {
            """
            LEFT JOIN lineage_relationships lr ON lt.id = lr.task_id
            LEFT JOIN lineage_tables source_table ON lr.source_table_id = source_table.id
            LEFT JOIN lineage_tables target_table ON lr.target_table_id = target_table.id
            """.trimIndent()
        } else {
            ""
        }

        criteria.statuses?.takeIf { it.isNotEmpty() }?.let { 
            val placeholders = it.joinToString(",") { "?" }
            conditions.add("lt.task_status IN ($placeholders)")
        }
        criteria.taskType?.let { conditions.add("lt.task_type = ?") }
        criteria.taskName?.let { conditions.add("lt.task_name LIKE ?") }
        criteria.createdBy?.let { conditions.add("lt.created_by = ?") }
        criteria.dateFrom?.let { conditions.add("lt.updated_at >= ?") }
        criteria.dateTo?.let { conditions.add("lt.updated_at <= ?") }
        criteria.isEnabled?.let { conditions.add("lt.is_enabled = ?") }
        criteria.batchId?.let { conditions.add("lt.batch_id = ?") }
        criteria.sourceId?.let { conditions.add("(source_table.datasource_id = ? OR target_table.datasource_id = ?)") }

        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else {
            ""
        }

        return Pair(whereClause, joinClause)
    }

    /**
     * 构建ORDER BY子句 (Build ORDER BY clause)
     */
    private fun buildOrderClause(pageable: Pageable): String {
        if (pageable.sort.isUnsorted) {
            return "ORDER BY created_at DESC"
        }

        val orderFields = pageable.sort.map { order ->
            val columnName = mapPropertyToColumn(order.property)
            "$columnName ${order.direction.name}"
        }.joinToString(", ")

        return "ORDER BY $orderFields"
    }

    /**
     * 将属性名映射到数据库列名 (Map property name to database column name)
     */
    private fun mapPropertyToColumn(propertyName: String): String {
        return when (propertyName) {
            "id" -> "lt.id"
            "taskName" -> "lt.task_name"
            "taskType" -> "lt.task_type"
            "sourceType" -> "lt.source_type"
            "sourceIdentifier" -> "lt.source_identifier"
            "sourceContent" -> "lt.source_content"
            "taskStatus" -> "lt.task_status"
            "scheduleType" -> "lt.schedule_type"
            "cronExpression" -> "lt.cron_expression"
            "isEnabled" -> "lt.is_enabled"
            "createdBy" -> "lt.created_by"
            "executedAt" -> "lt.executed_at"
            "completedAt" -> "lt.completed_at"
            "errorMessage" -> "lt.error_message"
            "createdAt" -> "lt.created_at"
            "updatedAt" -> "lt.updated_at"
            "jobKey" -> "lt.job_key"
            "processingTimeMs" -> "lt.processing_time_ms"
            "hasChanges" -> "lt.has_changes"
            "batchId" -> "lt.batch_id"
            "executionCount" -> "lt.execution_count"
            "lastExecutionId" -> "lt.last_execution_id"
            else -> "lt.$propertyName" // 默认返回原属性名 (fallback to original property name)
        }
    }

    /**
     * 构建查询参数 (Build query parameters)
     */
    private fun buildParameters(criteria: TaskQueryCriteria): List<Any> {
        val params = mutableListOf<Any>()

        criteria.statuses?.takeIf { it.isNotEmpty() }?.forEach { status ->
            params.add(status.name)
        }
        criteria.taskType?.let { params.add(it.name) }
        criteria.taskName?.let { params.add("%$it%") }
        criteria.createdBy?.let { params.add(it) }
        criteria.dateFrom?.let { params.add(it) }
        criteria.dateTo?.let { params.add(it) }
        criteria.isEnabled?.let { params.add(it) }
        criteria.batchId?.let { params.add(it) }
        criteria.sourceId?.let { 
            params.add(it) // for source_table.datasource_id
            params.add(it) // for target_table.datasource_id
        }

        return params
    }

    /**
     * 更新任务状态和执行信息 (Update task status and execution info)
     */
    fun updateTaskExecution(
        taskId: Long,
        status: TaskStatus,
        executedAt: LocalDateTime?,
        completedAt: LocalDateTime?,
        processingTimeMs: Long?,
        hasChanges: Boolean?,
        errorMessage: String?,
        executionId: String?
    ): Int {
        val sql = """
            UPDATE lineage_tasks 
            SET task_status = ?, 
                executed_at = ?, 
                completed_at = ?, 
                processing_time_ms = ?, 
                has_changes = ?, 
                error_message = ?, 
                last_execution_id = ?,
                execution_count = execution_count + 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.update(
            sql,
            status.name,
            executedAt,
            completedAt,
            processingTimeMs,
            hasChanges,
            errorMessage,
            executionId,
            taskId
        )
    }

    /**
     * 根据作业键查找所有处理历史 (Find all processing history by job key)
     * 
     * UC-07: 支持查看特定任务的处理历史
     * 
     * @param jobKey 作业键
     * @return 处理历史列表
     */
    fun findAllProcessingHistoryByJobKey(jobKey: String): List<JobLineageHashHistory> {
        val sql = """
            SELECT id, job_key, job_type, reader_job_id, write_job_id, processed_at,
                   processing_result, changes_detected, processing_duration_ms, 
                   lineage_hash, error_message
            FROM lineage_job_processing_history 
            WHERE job_key = ?
            ORDER BY processed_at DESC
        """.trimIndent()

        return jdbcTemplate.query(sql, jobLineageHashHistoryRowMapper, jobKey)
    }

    /**
     * 更新任务启用状态 (Update task enabled status)
     * 
     * 用于停用不再活跃的数据交换作业对应的血缘任务
     */
    fun updateTaskStatus(
        taskId: Long,
        isEnabled: Boolean,
        updatedBy: String,
        reason: String
    ): Int {
        val sql = """
            UPDATE lineage_tasks 
            SET is_enabled = ?,
                task_status = ?,
                error_message = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()

        // 如果禁用任务，则将状态设置为 DISABLED
        val status = if (!isEnabled) TaskStatus.DISABLED else TaskStatus.PENDING

        return jdbcTemplate.update(
            sql,
            isEnabled,
            status.name,
            reason,
            taskId
        )
    }

    /**
     * 根据源系统ID和目标系统ID查询表对关系 (Find table pairs by source and target system IDs)
     * 
     * @param sourceSystemId 源系统ID
     * @param targetSystemId 目标系统ID
     * @return 表对关系列表
     */
    fun findTablePairsBySystemIds(sourceSystemId: Long, targetSystemId: Long): List<TablePairDto> {
        val sql = """
            SELECT 
                st.table_name as source_table,
                st.schema_name as source_schema,
                sds.datasource_name as source_datasource,
                sds.system_id as source_system_id,
                ssys.system_name as source_system_name,
                tt.table_name as target_table,
                tt.schema_name as target_schema,
                tds.datasource_name as target_datasource,
                tds.system_id as target_system_id,
                tsys.system_name as target_system_name,
                lr.lineage_type,
                lr.confidence_score,
                COUNT(*) as relationship_count,
                MAX(lr.created_at) as latest_created_at
            FROM lineage_relationships lr
            JOIN lineage_tables st ON lr.source_table_id = st.id
            JOIN lineage_tables tt ON lr.target_table_id = tt.id
            JOIN lineage_datasources sds ON st.datasource_id = sds.id
            JOIN lineage_datasources tds ON tt.datasource_id = tds.id
            LEFT JOIN lineage_systems ssys ON sds.system_id = ssys.id
            LEFT JOIN lineage_systems tsys ON tds.system_id = tsys.id
            WHERE lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
              AND sds.system_id = ?
              AND tds.system_id = ?
            GROUP BY 
                st.table_name, st.schema_name, sds.datasource_name, sds.system_id, ssys.system_name,
                tt.table_name, tt.schema_name, tds.datasource_name, tds.system_id, tsys.system_name,
                lr.lineage_type, lr.confidence_score
            ORDER BY latest_created_at DESC, st.table_name, tt.table_name
        """.trimIndent()

        return jdbcTemplate.query(sql, tablePairRowMapper, sourceSystemId, targetSystemId)
    }

    /**
     * 表对关系行映射器 (Table Pair Row Mapper)
     */
    private val tablePairRowMapper = RowMapper<TablePairDto> { rs, _ ->
        TablePairDto(
            sourceTable = rs.getString("source_table"),
            sourceSchema = rs.getString("source_schema"),
            sourceDatasource = rs.getString("source_datasource"),
            sourceSystemId = rs.getObject("source_system_id") as? Long,
            sourceSystemName = rs.getString("source_system_name"),
            targetTable = rs.getString("target_table"),
            targetSchema = rs.getString("target_schema"),
            targetDatasource = rs.getString("target_datasource"),
            targetSystemId = rs.getObject("target_system_id") as? Long,
            targetSystemName = rs.getString("target_system_name"),
            lineageType = rs.getString("lineage_type"),
            confidenceScore = rs.getBigDecimal("confidence_score"),
            relationshipCount = rs.getInt("relationship_count"),
            createdAt = rs.getTimestamp("latest_created_at")?.toLocalDateTime()?.toString()
        )
    }

    /**
     * 血缘任务行映射器 (Lineage Task Row Mapper)
     */
    private class LineageTaskRowMapper : RowMapper<LineageTask> {
        override fun mapRow(rs: ResultSet, rowNum: Int): LineageTask {
            return LineageTask(
                id = rs.getLong("id"),
                taskName = rs.getString("task_name"),
                taskType = TaskType.valueOf(rs.getString("task_type")),
                sourceType = SourceType.valueOf(rs.getString("source_type")),
                sourceIdentifier = rs.getString("source_identifier"),
                sourceContent = rs.getString("source_content"),
                taskStatus = TaskStatus.valueOf(rs.getString("task_status")),
                scheduleType = ScheduleType.valueOf(rs.getString("schedule_type")),
                cronExpression = rs.getString("cron_expression"),
                isEnabled = rs.getBoolean("is_enabled"),
                createdBy = rs.getString("created_by"),
                executedAt = rs.getTimestamp("executed_at")?.toLocalDateTime(),
                completedAt = rs.getTimestamp("completed_at")?.toLocalDateTime(),
                errorMessage = rs.getString("error_message"),
                createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
                updatedAt = rs.getTimestamp("updated_at").toLocalDateTime(),
                jobKey = rs.getString("job_key"),
                processingTimeMs = rs.getObject("processing_time_ms") as? Long,
                hasChanges = rs.getBoolean("has_changes"),
                batchId = rs.getString("batch_id"),
                executionCount = rs.getInt("execution_count"),
                lastExecutionId = rs.getString("last_execution_id")
            )
        }
    }
} 
