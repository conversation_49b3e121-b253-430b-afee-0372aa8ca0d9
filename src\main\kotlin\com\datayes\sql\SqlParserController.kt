package com.datayes.sql

import com.datayes.script.ParsedScriptLineage
import com.datayes.script.ScriptAnalysisService
import com.datayes.script.ScriptType
import org.springframework.web.bind.annotation.*

/**
 * SQL解析器控制器 (SQL Parser Controller)
 * 提供SQL解析的REST API接口
 */
@RestController
@RequestMapping("/api/sql")
class SqlParserController {

    /**
     * 解析SQL查询字符串
     *
     * @param sql SQL查询字符串（纯文本）
     * @return SQL解析结果
     */
    @PostMapping("/parse", consumes = ["text/plain"])
    fun parseQuery(@RequestBody sql: String): ParsedScriptLineage {
        return ScriptAnalysisService.parseScriptToLineage(
            scriptContent = sql,
            scriptType = ScriptType.SQL,
            scriptName = "test_001"
        )
    }
}