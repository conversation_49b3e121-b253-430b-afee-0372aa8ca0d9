package com.datayes.metadata

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

/**
 * 元数据数据源连接信息实体 (Metadata Data Source Connection Info Entity)
 *
 * 对应 metadata_data_source_connection_info 表的实体类，用于存储数据源连接配置信息
 */
@Table("metadata_data_source_connection_info")
data class MetadataDataSourceConnectionInfo(

    @Id
    val id: Long? = null,
    
    @Column("DATA_SOURCE_ID")
    val dataSourceId: Long,
    
    @Column("DB_DRIVER")
    val dbDriver: String,
    
    @Column("DB_NAME")
    val dbName: String,
    
    @Column("DB_URL")
    val dbUrl: String?,
    
    @Column("DB_PORT")
    val dbPort: Int?,
    
    @Column("DB_USERNAME")
    val dbUsername: String,
    
    @Column("DB_PASSWORD")
    val dbPassword: String,
    
    @Column("CUSTOM_JDBC_URL")
    val customJdbcUrl: String?,
    
    @Column("IS_COLLECT_DB")
    val isCollectDb: Boolean = false,
    
    @Column("CONNECTION_DESC")
    val connectionDesc: String?,
    
    @Column("ACTIVE_FLAG")
    val activeFlag: Boolean = true,
    
    @Column("CREATE_BY")
    val createBy: String?,
    
    @Column("CREATE_TIME")
    val createTime: LocalDateTime?,
    
    @Column("UPDATE_BY")
    val updateBy: String?,
    
    @Column("UPDATE_TIME")
    val updateTime: LocalDateTime?
)