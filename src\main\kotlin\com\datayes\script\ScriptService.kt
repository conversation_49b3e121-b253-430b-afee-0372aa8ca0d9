package com.datayes.script

import com.datayes.storage.S3StorageService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.security.MessageDigest
import java.time.LocalDateTime

/**
 * 脚本服务 (Script Service)
 *
 * 实现脚本上传和管理的业务逻辑
 */
@Service
class ScriptService(
    private val scriptRepository: ScriptRepository,
    private val scriptAnalysisService: ScriptAnalysisService,
    private val s3StorageService: S3StorageService
) {

    private val logger = LoggerFactory.getLogger(ScriptService::class.java)

    /**
     * 上传脚本文件 (Upload script file)
     *
     * UC-1: 处理脚本文件上传，包括验证、去重、存储
     */
    fun uploadScript(file: MultipartFile, uploadUser: String): ScriptUploadResult {
        logger.info("f9e8d7c6 | 开始处理脚本上传: fileName=${file.originalFilename}, uploadUser=$uploadUser")

        try {
            // 1. 验证文件 (Validate file)
            validateUploadedFile(file)

            // 2. 读取文件内容 (Read file content)
            val scriptContent = file.inputStream.bufferedReader().use { it.readText() }
            if (scriptContent.isBlank()) {
                throw IllegalArgumentException("脚本文件内容不能为空")
            }

            // 3. 确定脚本类型 (Determine script type)
            val scriptType = determineScriptType(file.originalFilename!!)

            // 4. 验证SQL脚本不包含多条语句 (Validate SQL script does not contain multiple statements)
            if (scriptType == ScriptType.SQL) {
                ScriptService.validateSingleSqlStatement(scriptContent)
            }

            // 5. 计算文件哈希 (Calculate file hash)
            val fileHash = calculateFileHash(scriptContent)

            // 6. 检查重复文件 (Check for duplicate files)
            val existingScript = scriptRepository.findByFileHash(fileHash)
            if (existingScript != null) {
                logger.warn("b5a4e3f2 | 发现重复文件: fileHash=$fileHash, existingScriptId=${existingScript.id}")
                return ScriptUploadResult(
                    success = false,
                    message = "文件已存在，相同内容的脚本已上传",
                    scriptId = existingScript.id,
                    duplicateScript = existingScript
                )
            }

            // 7. 上传文件到S3 (Upload file to S3)
            val s3UploadResult = s3StorageService.uploadFile(
                fileName = file.originalFilename!!,
                content = file.bytes,
                contentType = file.contentType ?: "text/plain"
            )

            if (!s3UploadResult.success) {
                logger.warn("b3c4d5e6 | S3上传失败，继续使用数据库存储: ${s3UploadResult.errorMessage}")
            }

            // 8. 创建脚本实体 (Create script entity)
            val script = UploadedScript(
                scriptName = file.originalFilename!!,
                scriptType = scriptType,
                filePath = s3UploadResult.fileKey,
                fileSize = file.size,
                fileHash = fileHash,
                scriptContent = scriptContent,
                uploadUser = uploadUser,
                analysisStatus = AnalysisStatus.PENDING,
                createdAt = LocalDateTime.now(),
                updatedAt = LocalDateTime.now()
            )

            // 9. 保存到数据库 (Save to database)
            val savedScript = scriptRepository.save(script)

            logger.info("c8f2a9e1 | 脚本上传成功: scriptId=${savedScript.id}, scriptType=${savedScript.scriptType}")

            // 10. 触发异步分析 (Trigger async analysis)
            val scriptId = savedScript.id ?: throw IllegalStateException("Saved script should have non-null id")
            scriptAnalysisService.triggerScriptAnalysis(scriptId)

            return ScriptUploadResult(
                success = true,
                message = "脚本上传成功",
                scriptId = scriptId,
                uploadedScript = savedScript
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("d7e6f5a4 | 脚本上传参数错误: ${e.message}")
            return ScriptUploadResult(
                success = false,
                message = e.message ?: "参数错误 (Invalid parameters)"
            )
        } catch (e: Exception) {
            logger.error("a3b2c1d0 | 脚本上传失败", e)
            return ScriptUploadResult(
                success = false,
                message = "上传失败: ${e.message}"
            )
        }
    }

    /**
     * 查询脚本列表 (Query script list)
     *
     * UC-2: 分页查询已上传脚本的列表，支持筛选
     */
    fun queryScripts(
        scriptName: String? = null,
        uploadUser: String? = null,
        startDate: LocalDateTime? = null,
        endDate: LocalDateTime? = null,
        scriptType: ScriptType? = null,
        analysisStatus: AnalysisStatus? = null,
        page: Int = 0,
        size: Int = 20
    ): ScriptQueryResult {
        logger.info("e4f3g2h1 | 查询脚本列表: page=$page, size=$size")

        // 参数验证 (Parameter validation)
        if (page < 0) throw IllegalArgumentException("页码不能小于0")
        if (size <= 0 || size > 100) throw IllegalArgumentException("每页大小必须在1-100之间")

        return scriptRepository.findScripts(
            scriptName = scriptName,
            uploadUser = uploadUser,
            startDate = startDate,
            endDate = endDate,
            scriptType = scriptType,
            analysisStatus = analysisStatus,
            page = page,
            size = size
        )
    }

    /**
     * 根据ID获取脚本详情 (Get script details by ID)
     */
    fun getScriptById(id: Long): UploadedScript? {
        logger.info("f8e7d6c5 | 查询脚本详情: scriptId=$id")
        return scriptRepository.findById(id)
    }

    /**
     * 删除脚本 (Delete script)
     *
     * UC-4: 删除已上传的脚本及其元数据记录
     */
    fun deleteScript(id: Long): ScriptDeleteResult {
        logger.info("g9h8i7j6 | 删除脚本: scriptId=$id")

        try {
            // 检查脚本是否存在 (Check if script exists)
            val script = scriptRepository.findById(id)
            if (script == null) {
                return ScriptDeleteResult(
                    success = false,
                    message = "脚本不存在"
                )
            }

            // 执行删除 (Perform deletion)
            val deleted = scriptRepository.deleteById(id)

            return if (deleted) {
                logger.info("a1b2c3d4 | 脚本删除成功: scriptId=$id")
                ScriptDeleteResult(
                    success = true,
                    message = "脚本删除成功"
                )
            } else {
                ScriptDeleteResult(
                    success = false,
                    message = "脚本删除失败"
                )
            }

        } catch (e: Exception) {
            logger.error("e5f6g7h8 | 删除脚本时发生错误: scriptId=$id", e)
            return ScriptDeleteResult(
                success = false,
                message = "删除失败: ${e.message}"
            )
        }
    }

    /**
     * 获取脚本内容 (Get script content from S3)
     */
    fun getScriptContent(script: UploadedScript): String {
        if (script.filePath == null) {
            throw IllegalStateException("脚本的S3文件键为空 (Script S3 file key is null)")
        }

        val downloadResult = s3StorageService.downloadFile(script.filePath)
        if (!downloadResult.success || downloadResult.content == null) {
            throw RuntimeException("从S3下载脚本内容失败: ${downloadResult.errorMessage}")
        }

        return String(downloadResult.content)
    }

    /**
     * 验证上传的文件 (Validate uploaded file)
     */
    private fun validateUploadedFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("上传文件不能为空")
        }

        val fileName = file.originalFilename
        if (fileName.isNullOrBlank()) {
            throw IllegalArgumentException("文件名不能为空")
        }

        // 文件大小限制 10MB (File size limit)
        val maxFileSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxFileSize) {
            throw IllegalArgumentException(
                "文件大小不能超过10MB，当前文件大小: ${file.size / 1024 / 1024}MB"
            )
        }

        // 文件扩展名验证 (File extension validation)
        val supportedExtensions = listOf(".sql", ".sh")
        val hasValidExtension = supportedExtensions.any { fileName.endsWith(it, ignoreCase = true) }
        if (!hasValidExtension) {
            throw IllegalArgumentException(
                "不支持的文件格式: $fileName. 仅支持 ${supportedExtensions.joinToString(", ")} 格式"
            )
        }

        logger.info("i9j8k7l6 | 文件验证通过: fileName=$fileName, size=${file.size}")
    }

    /**
     * 确定脚本类型 (Determine script type)
     */
    private fun determineScriptType(fileName: String): ScriptType {
        return when {
            fileName.endsWith(".sql", ignoreCase = true) -> ScriptType.SQL
            fileName.endsWith(".sh", ignoreCase = true) -> ScriptType.SHELL
            else -> throw IllegalArgumentException("无法确定脚本类型: $fileName")
        }
    }


    /**
     * 计算文件哈希值 (Calculate file hash)
     */
    private fun calculateFileHash(content: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(content.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }

    companion object {
        /**
         * 验证SQL脚本只包含单条语句 (Validate SQL script contains only a single statement)
         */
        fun validateSingleSqlStatement(scriptContent: String) {
            // 清理和规范化SQL内容
            val normalizedSql = scriptContent
                .replace("\\n", "\n")
                .replace("\\t", "\t")
                .trim()
            
            // 检查是否包含多个分号分隔的语句
            // 首先简单检查：去除注释和字符串字面量后计算分号数量
            val cleanedSql = removeSqlCommentsAndStrings(normalizedSql)
            val semicolonCount = cleanedSql.count { it == ';' }
            
            // 如果有多个分号，可能是多条语句
            if (semicolonCount > 1) {
                throw IllegalArgumentException(
                    "SQL脚本不能包含多条语句，检测到 $semicolonCount 个分号"
                )
            }
            
            // 如果只有一个分号在末尾，这是可以接受的
            if (semicolonCount == 1 && !cleanedSql.trimEnd().endsWith(";")) {
                throw IllegalArgumentException(
                    "SQL脚本不能包含多条语句，分号不在语句末尾"
                )
            }
        }

        /**
         * 移除SQL注释和字符串字面量，用于准确计算分号数量
         */
        private fun removeSqlCommentsAndStrings(sql: String): String {
            val result = StringBuilder()
            var i = 0
            var inSingleQuote = false
            var inDoubleQuote = false
            var inLineComment = false
            var inBlockComment = false
            
            while (i < sql.length) {
                val char = sql[i]
                val nextChar = if (i + 1 < sql.length) sql[i + 1] else '\u0000'
                val prevChar = if (i > 0) sql[i - 1] else '\u0000'
                
                when {
                    // 处理转义字符：如果前一个字符是反斜杠，跳过当前字符
                    prevChar == '\\' -> {
                        result.append(' ') // 用空格替换转义字符
                    }
                    
                    // 处理字符串字面量（单引号）
                    char == '\'' && !inDoubleQuote && !inLineComment && !inBlockComment -> {
                        inSingleQuote = !inSingleQuote
                        result.append(' ') // 用空格替换字符串内容
                    }
                    
                    // 处理字符串字面量（双引号）
                    char == '"' && !inSingleQuote && !inLineComment && !inBlockComment -> {
                        inDoubleQuote = !inDoubleQuote
                        result.append(' ') // 用空格替换字符串内容
                    }
                    
                    // 处理注释
                    char == '-' && nextChar == '-' && !inSingleQuote && !inDoubleQuote && !inBlockComment -> {
                        inLineComment = true
                        i++ // 跳过下一个字符
                        result.append(' ')
                    }
                    char == '/' && nextChar == '*' && !inSingleQuote && !inDoubleQuote && !inLineComment -> {
                        inBlockComment = true
                        i++ // 跳过下一个字符
                        result.append(' ')
                    }
                    char == '*' && nextChar == '/' && inBlockComment -> {
                        inBlockComment = false
                        i++ // 跳过下一个字符
                        result.append(' ')
                    }
                    char == '\n' -> {
                        inLineComment = false // 行注释在换行处结束
                        result.append(char)
                    }
                    
                    // 在字符串或注释中，用空格替换
                    inSingleQuote || inDoubleQuote || inLineComment || inBlockComment -> {
                        result.append(' ')
                    }
                    
                    // 正常字符
                    else -> {
                        result.append(char)
                    }
                }
                i++
            }
            
            return result.toString()
        }
    }
}

/**
 * 脚本上传结果 (Script Upload Result)
 */
data class ScriptUploadResult(
    val success: Boolean,
    val message: String,
    val scriptId: Long? = null,
    val uploadedScript: UploadedScript? = null,
    val duplicateScript: UploadedScript? = null
)

/**
 * 脚本删除结果 (Script Delete Result)
 */
data class ScriptDeleteResult(
    val success: Boolean,
    val message: String
) 